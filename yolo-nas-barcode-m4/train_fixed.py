# Fix for PyTorch 2.6+ weights_only security issue
import torch
original_torch_load = torch.load
def patched_torch_load(*args, **kwargs):
    kwargs["weights_only"] = False
    return original_torch_load(*args, **kwargs)
torch.load = patched_torch_load
print("[INFO] ✅ PyTorch 2.6+ compatibility patch applied")

# Import and run the original train script
import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Modify sys.argv to handle "none" weights
if len(sys.argv) > 1:
    for i, arg in enumerate(sys.argv):
        if arg == "--weight" and i + 1 < len(sys.argv):
            if sys.argv[i + 1].lower() in ["none", "null", "false"]:
                sys.argv[i + 1] = "imagenet"  # Use imagenet instead of none
                print("[INFO] Using imagenet weights instead of none (safer option)")

# Now import and execute the original train script
exec(open('train.py').read())

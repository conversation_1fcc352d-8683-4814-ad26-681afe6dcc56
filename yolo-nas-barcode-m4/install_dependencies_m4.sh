#!/bin/bash

# Dependencies Installation for Mac Mini M4 (Apple Silicon)
# Optimized for ARM64 with MPS backend support

set -e

echo "🔧 Installing dependencies for Mac Mini M4..."
echo "Using UV package manager for fast installation"

# Activate UV environment
echo "📦 Setting up UV virtual environment..."
uv venv --python 3.11
source .venv/bin/activate

# Verify Python version and architecture
echo "🐍 Python version: $(python --version)"
echo "🏗️  Architecture: $(python -c 'import platform; print(platform.machine())')"

# Install PyTorch with MPS support for Apple Silicon
echo "⚡ Installing PyTorch with MPS support..."
uv add torch torchvision torchaudio

# Verify MPS availability
echo "🧪 Testing MPS availability..."
python -c "
import torch
print(f'PyTorch version: {torch.__version__}')
print(f'MPS available: {torch.backends.mps.is_available()}')
print(f'MPS built: {torch.backends.mps.is_built()}')
if torch.backends.mps.is_available():
    print('✅ MPS backend ready for GPU acceleration!')
else:
    print('⚠️  MPS not available, will use CPU')
"

# Install Super-Gradients (compatible with Apple Silicon)
echo "🎯 Installing Super-Gradients..."
uv add super-gradients==3.1.3

# Install additional dependencies optimized for Apple Silicon
echo "📚 Installing additional dependencies..."
uv add \
    pyyaml \
    opencv-python \
    pillow \
    matplotlib \
    seaborn \
    tqdm \
    numpy \
    scipy \
    scikit-learn

# Install development and optimization tools
echo "🛠️  Installing development tools..."
uv add \
    jupyter \
    ipython \
    tensorboard

# Create requirements.txt for reference
echo "📝 Creating requirements.txt..."
uv pip freeze > requirements.txt

echo "✅ All dependencies installed successfully!"
echo ""
echo "🧪 Testing installation..."
python -c "
import torch
import super_gradients
import cv2
import yaml
print('✅ All imports successful!')
print(f'PyTorch: {torch.__version__}')
print(f'Super-Gradients: {super_gradients.__version__}')
print(f'OpenCV: {cv2.__version__}')
"

echo ""
echo "🎉 Environment ready for YOLO-NAS training on Mac Mini M4!"
echo "💡 To activate environment: source .venv/bin/activate"

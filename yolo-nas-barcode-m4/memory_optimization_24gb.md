# Mac Mini M4 24GB Memory Optimization Guide

## 🚀 Memory Advantage Overview

Your Mac Mini M4 with 24GB unified memory provides significant advantages for YOLO-NAS training:

### Key Benefits
- **Large Batch Training**: Use batch sizes 12-16 vs typical 4-6
- **Faster Convergence**: Larger batches provide better gradient estimates
- **Reduced Training Time**: ~40-50% faster than smaller batch training
- **Better Model Quality**: More stable training with larger batches

## 📊 Batch Size Recommendations

### Conservative Mode (Recommended First Run)
```bash
Batch Size: 12
Memory Usage: ~18-20GB
Training Time: ~3-4 hours (200 epochs)
Stability: High
```

### Aggressive Mode (Maximum Performance)
```bash
Batch Size: 16
Memory Usage: ~22-24GB
Training Time: ~2.5-3.5 hours (250 epochs)
Stability: Medium (requires monitoring)
```

### Fallback Mode (If Issues)
```bash
Batch Size: 8
Memory Usage: ~12-15GB
Training Time: ~4-5 hours (200 epochs)
Stability: Very High
```

## 🎯 Training Scripts Comparison

| Script | Batch Size | Epochs | Memory Usage | Training Time | Use Case |
|--------|------------|--------|--------------|---------------|----------|
| `train_barcode_m4.sh` | 12 | 200 | ~18-20GB | 3-4 hours | Recommended |
| `train_barcode_m4_aggressive.sh` | 16 | 250 | ~22-24GB | 2.5-3.5 hours | Maximum performance |

## 💡 Memory Optimization Tips

### Before Training
1. **Close Unnecessary Apps**: Free up memory for training
2. **Check Available Memory**: Use Activity Monitor
3. **Disable Background Processes**: Pause cloud sync, etc.

### During Training
1. **Monitor Memory Pressure**: Use Activity Monitor
2. **Watch Temperature**: Use iStat Menus or similar
3. **Check MPS Utilization**: Ensure GPU is being used

### Memory Monitoring Commands
```bash
# Check memory usage
vm_stat

# Monitor in real-time
top -o MEM

# Check MPS usage
sudo powermetrics -n 1 -i 1000 | grep -A 20 "GPU"
```

## 🔧 Troubleshooting 24GB Setup

### Out of Memory Errors
```bash
# Reduce batch size
BATCH_SIZE=8  # Instead of 12 or 16

# Reduce workers
WORKERS=6     # Instead of 8 or 10

# Reduce image size (if needed)
IMAGE_SIZE=320  # Instead of 416
```

### Thermal Throttling
```bash
# Signs of thermal throttling:
- Training suddenly slows down
- CPU frequency drops
- Fan noise increases significantly

# Solutions:
- Ensure good ventilation
- Lower batch size slightly
- Take breaks between training runs
```

### MPS Issues
```bash
# Check MPS availability
python -c "import torch; print(torch.backends.mps.is_available())"

# Force CPU if MPS issues
python train.py --cpu  # Add --cpu flag
```

## 📈 Performance Expectations

### With 24GB Memory Advantage
- **Batch Size 12**: 40% faster than batch size 6
- **Batch Size 16**: 60% faster than batch size 6
- **Better Convergence**: Larger batches = more stable gradients
- **Higher Quality**: Better final model accuracy

### Training Time Comparison
| Batch Size | 200 Epochs | 250 Epochs | Memory Usage |
|------------|------------|------------|--------------|
| 6 (typical) | 5-6 hours | 6-7 hours | ~12GB |
| 12 (24GB) | 3-4 hours | 4-5 hours | ~18GB |
| 16 (24GB max) | 2.5-3.5 hours | 3-4 hours | ~22GB |

## 🎛️ Advanced Configuration

### Custom Batch Size Testing
```bash
# Test different batch sizes
for batch in 8 10 12 14 16; do
    echo "Testing batch size: $batch"
    python train.py --data data.yaml --batch $batch --epoch 5 --mps
done
```

### Memory-Optimized Training Parameters
```bash
# For maximum memory efficiency
python train.py \
    --data data.yaml \
    --model yolo_nas_s \
    --batch 16 \
    --epoch 250 \
    --size 416 \
    --worker 10 \
    --initial_lr 0.0015 \
    --weight_decay 0.0005 \
    --warmup_epochs 8 \
    --mps
```

## 🏆 Best Practices for 24GB

1. **Start Conservative**: Use batch size 12 first
2. **Monitor Resources**: Watch memory and temperature
3. **Scale Up Gradually**: Increase batch size if stable
4. **Use MPS**: Always enable MPS for GPU acceleration
5. **Plan Training Time**: Large batches train faster but use more power

## 🚨 Warning Signs

Watch for these issues with large batch training:

### Memory Pressure
- System becomes unresponsive
- Other apps crash or slow down
- Swap usage increases significantly

### Thermal Issues
- Fan runs at maximum speed constantly
- Training speed suddenly decreases
- System feels very hot

### Solutions
- Reduce batch size by 2-4
- Increase cooling/ventilation
- Take breaks between training runs
- Monitor with Activity Monitor

## 🎯 Recommended Workflow

1. **First Run**: Use conservative script (batch 12)
2. **Monitor**: Check memory and temperature
3. **Optimize**: If stable, try aggressive script (batch 16)
4. **Compare**: Evaluate training time vs stability
5. **Deploy**: Use the best performing model

Your 24GB Mac Mini M4 is exceptionally well-suited for YOLO-NAS training - take advantage of it! 🚀

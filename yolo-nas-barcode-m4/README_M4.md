# YOLO-NAS S Barcode Detection - Mac Mini M4 Setup

This guide provides complete setup instructions for training YOLO-NAS S on Mac Mini M4 (Apple Silicon) for barcode detection.

## 🚀 Quick Start

### Prerequisites
- Mac Mini M4 (Apple Silicon)
- macOS 13.0+ (Ventura) or later
- UV package manager installed
- At least 8GB RAM (16GB+ recommended)
- 20GB+ free storage

### 1. Environment Setup

```bash
# Clone and setup the project
git clone <your-repo-url>
cd YOLO-NAS

# Run the automated setup
chmod +x setup_m4_environment.sh
./setup_m4_environment.sh
```

### 2. Install Dependencies

```bash
# Navigate to the project directory
cd yolo-nas-barcode-m4

# Install dependencies optimized for Apple Silicon
chmod +x install_dependencies_m4.sh
./install_dependencies_m4.sh
```

### 3. System Check

```bash
# Verify your system is ready
python check_m4_system.py
```

### 4. Start Training

Choose your training mode based on your needs:

#### Conservative Training (Recommended for first run)
```bash
# Balanced training with batch size 12
chmod +x train_barcode_m4.sh
./train_barcode_m4.sh
```

#### Aggressive Training (Maximum 24GB utilization)
```bash
# Maximum performance training with batch size 16
chmod +x train_barcode_m4_aggressive.sh
./train_barcode_m4_aggressive.sh
```

## 📊 Mac Mini M4 Optimizations

### Hardware Specifications
- **CPU**: Apple M4 chip (10-core CPU)
- **GPU**: 10-core GPU with MPS support
- **Memory**: 24GB Unified memory architecture
- **Architecture**: ARM64

### Training Optimizations (24GB Memory)
- **Batch Size**: 12-16 (optimized for 24GB memory advantage)
- **Image Size**: 416x416 (mobile-optimized)
- **Backend**: MPS (Metal Performance Shaders)
- **Workers**: 8-10 (maximum M4 utilization)
- **Mixed Precision**: Enabled for faster training
- **Memory Advantage**: Large batch training for better convergence

### Expected Performance (24GB)
- **Training Speed**: ~2.5-4 hours for 200 epochs with MPS (large batch advantage)
- **Model Size**: ~15-20MB after optimization
- **Inference Speed**: ~30-50ms on mobile devices
- **Training Advantage**: Faster convergence with larger batches

## 🛠️ Manual Setup (Alternative)

If you prefer manual setup:

### 1. Create UV Environment
```bash
uv init yolo-nas-barcode-m4 --python 3.11
cd yolo-nas-barcode-m4
uv venv --python 3.11
source .venv/bin/activate
```

### 2. Install PyTorch with MPS
```bash
uv add torch torchvision torchaudio
```

### 3. Install Super-Gradients
```bash
uv add super-gradients==3.1.3
```

### 4. Install Additional Dependencies
```bash
uv add pyyaml opencv-python pillow matplotlib seaborn tqdm numpy scipy scikit-learn tensorboard jupyter ipython
```

## 📁 Project Structure

```
yolo-nas-barcode-m4/
├── .venv/                          # UV virtual environment
├── Barcodes.v5i.coco/             # Your barcode dataset
│   ├── train/
│   ├── valid/
│   └── test/
├── data.yaml                       # Dataset configuration
├── train.py                        # Training script (MPS-enabled)
├── mobile_optimization.py          # Mobile model optimization
├── check_m4_system.py             # System verification
├── train_barcode_m4.sh            # Automated training script
├── pyproject.toml                  # UV project configuration
└── runs/                          # Training outputs
```

## 🎯 Training Commands

### Basic Training
```bash
python train.py \
    --data data.yaml \
    --model yolo_nas_s \
    --batch 4 \
    --epoch 150 \
    --size 416 \
    --mps
```

### Advanced Training with Optimization
```bash
python train.py \
    --data data.yaml \
    --model yolo_nas_s \
    --batch 6 \
    --epoch 200 \
    --size 416 \
    --name barcode_m4_optimized \
    --initial_lr 0.0005 \
    --lr_mode cosine \
    --optimizer AdamW \
    --weight_decay 0.0001 \
    --mps
```

## 📱 Mobile Optimization

After training, optimize for mobile deployment:

```bash
python mobile_optimization.py \
    --model_path runs/barcode_m4_v1/ckpt_best.pth \
    --model_type yolo_nas_s \
    --num_classes 3 \
    --output_dir mobile_models/barcode_v1
```

## 🔧 Troubleshooting

### Common Issues

1. **MPS Not Available**
   ```bash
   # Check MPS support
   python -c "import torch; print(torch.backends.mps.is_available())"
   ```

2. **Out of Memory**
   - Reduce batch size to 2-4
   - Use image size 320x320
   - Close other applications

3. **Slow Training**
   - Ensure MPS is enabled (`--mps` flag)
   - Check system temperature
   - Reduce batch size if thermal throttling

4. **UV Issues**
   ```bash
   # Reinstall UV
   curl -LsSf https://astral.sh/uv/install.sh | sh
   source $HOME/.cargo/env
   ```

### Performance Tips

1. **Monitor System Resources**
   ```bash
   # Check CPU/GPU usage
   sudo powermetrics -n 1 -i 1000
   ```

2. **Optimize for Long Training**
   - Enable "Prevent computer from sleeping" in Energy Saver
   - Ensure good ventilation
   - Monitor temperature with iStat Menus

3. **Memory Management**
   - Close unnecessary applications
   - Use Activity Monitor to check memory pressure

## 📈 Expected Results

### Training Metrics
- **mAP@0.5**: 85-95% (depending on dataset quality)
- **Training Loss**: Should decrease steadily
- **Validation Loss**: Should follow training loss

### Model Performance
- **Size**: ~15-20MB (optimized)
- **Speed**: 30-50ms inference on mobile
- **Accuracy**: >90% for clear barcode images

## 🎉 Next Steps

After successful training:

1. **Test the Model**
   ```bash
   python inference.py --model yolo_nas_s --weight runs/barcode_m4_v1/ckpt_best.pth --num 3 --source test_image.jpg
   ```

2. **Deploy to Mobile**
   - Use the optimized models in `mobile_models/`
   - Integrate with iOS/Android apps
   - Consider Core ML conversion for iOS

3. **Fine-tune if Needed**
   - Adjust hyperparameters
   - Add more training data
   - Experiment with different augmentations

## 📞 Support

For issues specific to Mac Mini M4 setup, check:
- Apple Developer Documentation for MPS
- PyTorch MPS documentation
- Super-Gradients GitHub issues

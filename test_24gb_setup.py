#!/usr/bin/env python3
"""
🧪 Test 24GB Mac Mini M4 Setup
Quick verification that MPS and aggressive parameters work
"""

import torch
import time
from super_gradients.training import models

def test_mps_performance():
    """Test MPS performance with different batch sizes"""
    print("🧪 Testing MPS Performance with Different Batch Sizes")
    print("=" * 60)
    
    if not torch.backends.mps.is_available():
        print("❌ MPS not available - cannot run performance test")
        return
    
    device = torch.device('mps')
    model = models.get('yolo_nas_s', num_classes=80)
    model = model.to(device)
    model.eval()
    
    batch_sizes = [8, 16, 32, 48, 64]
    image_size = 640
    
    print(f"Model: YOLO-NAS S")
    print(f"Image Size: {image_size}x{image_size}")
    print(f"Device: {device}")
    print("-" * 60)
    
    results = []
    
    for batch_size in batch_sizes:
        try:
            # Clear cache before each test
            torch.backends.mps.empty_cache()
            
            # Create test batch
            x = torch.randn(batch_size, 3, image_size, image_size).to(device)
            
            # Warmup
            with torch.no_grad():
                for _ in range(3):
                    _ = model(x)
            
            # Measure inference time
            torch.backends.mps.synchronize()
            start_time = time.time()
            
            with torch.no_grad():
                for _ in range(10):
                    output = model(x)
            
            torch.backends.mps.synchronize()
            end_time = time.time()
            
            avg_time = (end_time - start_time) / 10
            fps = batch_size / avg_time
            
            print(f"Batch {batch_size:2d}: {avg_time:.3f}s/batch, {fps:.1f} FPS ✅")
            results.append((batch_size, avg_time, fps))
            
        except Exception as e:
            print(f"Batch {batch_size:2d}: FAILED - {str(e)} ❌")
            break
    
    print("-" * 60)
    if results:
        best_batch, best_time, best_fps = max(results, key=lambda x: x[2])
        print(f"🏆 Best Performance: Batch {best_batch} ({best_fps:.1f} FPS)")
        
        # Recommend batch size for training (usually 60-70% of max)
        recommended_batch = int(best_batch * 0.7)
        print(f"💡 Recommended Training Batch: {recommended_batch}")
    
    print("=" * 60)

def test_memory_usage():
    """Test memory usage patterns"""
    print("🧠 Testing Memory Usage Patterns")
    print("=" * 60)
    
    if not torch.backends.mps.is_available():
        print("❌ MPS not available - cannot test memory")
        return
    
    device = torch.device('mps')
    
    # Test different model sizes
    models_to_test = ['yolo_nas_s', 'yolo_nas_m']
    
    for model_name in models_to_test:
        try:
            print(f"Testing {model_name.upper()}...")
            
            # Clear cache
            torch.backends.mps.empty_cache()
            
            # Load model
            model = models.get(model_name, num_classes=80)
            model = model.to(device)
            
            # Test with batch size 32
            batch_size = 32
            x = torch.randn(batch_size, 3, 640, 640).to(device)
            
            # Forward pass
            with torch.no_grad():
                output = model(x)
            
            print(f"✅ {model_name.upper()}: Batch {batch_size} successful")
            
        except Exception as e:
            print(f"❌ {model_name.upper()}: Failed - {str(e)}")
    
    print("=" * 60)

def test_dataloader_performance():
    """Test dataloader performance with different worker counts"""
    print("⚡ Testing DataLoader Performance")
    print("=" * 60)
    
    import torch.utils.data as data
    import numpy as np
    
    # Create dummy dataset
    class DummyDataset(data.Dataset):
        def __init__(self, size=1000):
            self.size = size
        
        def __len__(self):
            return self.size
        
        def __getitem__(self, idx):
            # Simulate image loading
            image = np.random.rand(3, 640, 640).astype(np.float32)
            label = np.random.randint(0, 80)
            return torch.from_numpy(image), label
    
    dataset = DummyDataset(1000)
    worker_counts = [0, 4, 8, 12, 16]
    batch_size = 32
    
    print(f"Dataset Size: {len(dataset)}")
    print(f"Batch Size: {batch_size}")
    print("-" * 60)
    
    for num_workers in worker_counts:
        try:
            dataloader = data.DataLoader(
                dataset, 
                batch_size=batch_size,
                num_workers=num_workers,
                pin_memory=True,
                persistent_workers=num_workers > 0
            )
            
            start_time = time.time()
            
            # Load 10 batches
            for i, (images, labels) in enumerate(dataloader):
                if i >= 10:
                    break
            
            end_time = time.time()
            avg_time = (end_time - start_time) / 10
            
            print(f"Workers {num_workers:2d}: {avg_time:.3f}s/batch ✅")
            
        except Exception as e:
            print(f"Workers {num_workers:2d}: FAILED - {str(e)} ❌")
    
    print("=" * 60)

def main():
    """Run all tests"""
    print("🚀 24GB Mac Mini M4 Setup Verification")
    print("=" * 80)
    print(f"PyTorch: {torch.__version__}")
    print(f"MPS Available: {torch.backends.mps.is_available()}")
    print(f"MPS Built: {torch.backends.mps.is_built()}")
    print("=" * 80)
    
    if torch.backends.mps.is_available():
        print("✅ MPS is available - running performance tests")
        test_mps_performance()
        test_memory_usage()
    else:
        print("⚠️  MPS not available - skipping MPS tests")
    
    test_dataloader_performance()
    
    print("🎉 Setup verification completed!")
    print("=" * 80)
    
    # Recommendations
    print("💡 RECOMMENDATIONS FOR 24GB TRAINING:")
    print("   • Use batch size 32-48 for YOLO-NAS S")
    print("   • Use 8-12 workers for optimal performance")
    print("   • Enable persistent_workers=True")
    print("   • Use pin_memory=True")
    print("   • Enable mixed_precision=True")
    print("=" * 80)

if __name__ == '__main__':
    main()

[project]
name = "yolo-nas-barcode-m4"
version = "1.0.0"
description = "YOLO-NAS S barcode detection optimized for Mac Mini M4"
authors = [
    {name = "Your Name", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.9,<3.12"
dependencies = [
    "torch>=2.0.0",
    "torchvision>=0.15.0",
    "torchaudio>=2.0.0",
    "super-gradients==3.1.3",
    "pyyaml>=6.0",
    "opencv-python>=4.8.0",
    "pillow>=9.5.0",
    "matplotlib>=3.7.0",
    "seaborn>=0.12.0",
    "tqdm>=4.65.0",
    "numpy>=1.24.0",
    "scipy>=1.10.0",
    "scikit-learn>=1.3.0",
    "tensorboard>=2.13.0",
    "jupyter>=1.0.0",
    "ipython>=8.14.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "black>=23.7.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.uv]
dev-dependencies = [
    "pytest>=7.4.0",
    "black>=23.7.0",
    "flake8>=6.0.0",
]

[tool.black]
line-length = 88
target-version = ['py39', 'py310', 'py311']

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true

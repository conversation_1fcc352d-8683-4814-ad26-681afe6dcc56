#!/bin/bash

# YOLO-NAS S Training Script for Mac Mini M4 (Apple Silicon)
# Optimized for ARM64 architecture with MPS backend

set -e

echo "🚀 Starting YOLO-NAS S training for barcode detection on Mac Mini M4..."

# Check if UV environment is activated
if [[ "$VIRTUAL_ENV" == "" ]]; then
    echo "📦 Activating UV virtual environment..."
    source .venv/bin/activate
fi

# Verify we're in the right environment
echo "🐍 Python: $(which python)"
echo "📍 Environment: $VIRTUAL_ENV"

# Check MPS availability
echo "🧪 Checking MPS availability..."
python -c "
import torch
print(f'PyTorch version: {torch.__version__}')
print(f'MPS available: {torch.backends.mps.is_available()}')
print(f'MPS built: {torch.backends.mps.is_built()}')
if torch.backends.mps.is_available():
    print('✅ MPS backend ready!')
    device = 'mps'
else:
    print('⚠️  MPS not available, using CPU')
    device = 'cpu'
"

# Training parameters optimized for Mac Mini M4 with 24GB memory
BATCH_SIZE=12  # Increased for 24GB unified memory
EPOCHS=200    # Extended training for better convergence
IMAGE_SIZE=416  # Mobile-optimized size
MODEL_TYPE="yolo_nas_s"
EXPERIMENT_NAME="barcode_m4_24gb_v1"

# Learning parameters optimized for Apple Silicon with large memory
INITIAL_LR=0.001  # Higher LR for larger batch size
WEIGHT_DECAY=0.0005
WARMUP_EPOCHS=5
WORKERS=8  # M4 has excellent multi-core performance, utilize more workers

echo ""
echo "🎯 Training Configuration for Mac Mini M4 (24GB):"
echo "- Model: $MODEL_TYPE"
echo "- Batch Size: $BATCH_SIZE (optimized for 24GB unified memory)"
echo "- Epochs: $EPOCHS"
echo "- Image Size: ${IMAGE_SIZE}x${IMAGE_SIZE}"
echo "- Experiment Name: $EXPERIMENT_NAME"
echo "- Initial LR: $INITIAL_LR"
echo "- Workers: $WORKERS"
echo "- Memory Advantage: Large batch training enabled"
echo ""

# Check if data.yaml exists
if [[ ! -f "data.yaml" ]]; then
    echo "❌ data.yaml not found. Please ensure it's in the current directory."
    exit 1
fi

# Check if dataset exists
if [[ ! -d "Barcodes.v5i.coco" ]]; then
    echo "❌ Barcodes.v5i.coco dataset not found. Please ensure it's in the current directory."
    exit 1
fi

echo "✅ Dataset found: Barcodes.v5i.coco"
echo "✅ Configuration file: data.yaml"
echo ""

# Start training with MPS backend
echo "🏃‍♂️ Starting training..."
python train.py \
    --data data.yaml \
    --model $MODEL_TYPE \
    --batch $BATCH_SIZE \
    --epoch $EPOCHS \
    --size $IMAGE_SIZE \
    --name $EXPERIMENT_NAME \
    --worker $WORKERS \
    --initial_lr $INITIAL_LR \
    --lr_mode cosine \
    --cosine_final_lr_ratio 0.01 \
    --optimizer AdamW \
    --weight_decay $WEIGHT_DECAY \
    --warmup_mode linear_epoch_step \
    --warmup_initial_lr 1e-6 \
    --lr_warmup_epochs $WARMUP_EPOCHS \
    --mps

# Check if training completed successfully
if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 Training completed successfully!"
    echo "📁 Best model saved at: runs/$EXPERIMENT_NAME/ckpt_best.pth"
    echo "📁 Latest model saved at: runs/$EXPERIMENT_NAME/ckpt_latest.pth"
    
    # Display model info
    echo ""
    echo "📊 Model Information:"
    python -c "
import os
import torch
from super_gradients.training import models

model_path = 'runs/$EXPERIMENT_NAME/ckpt_best.pth'
if os.path.exists(model_path):
    model = models.get('$MODEL_TYPE', num_classes=3, checkpoint_path=model_path)
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f'Total parameters: {total_params:,}')
    print(f'Trainable parameters: {trainable_params:,}')
    print(f'Model size: ~{total_params * 4 / 1024 / 1024:.1f} MB (FP32)')
else:
    print('Model file not found')
"
    
    # Run mobile optimization
    echo ""
    echo "📱 Optimizing model for mobile deployment..."
    python mobile_optimization.py \
        --model_path runs/$EXPERIMENT_NAME/ckpt_best.pth \
        --model_type $MODEL_TYPE \
        --num_classes 3 \
        --output_dir mobile_models/$EXPERIMENT_NAME
    
    echo ""
    echo "✅ Mobile optimization completed!"
    echo "📱 Mobile models available in: mobile_models/$EXPERIMENT_NAME/"
    echo ""
    echo "🎯 Next steps:"
    echo "1. Test the model: python inference.py --model $MODEL_TYPE --weight runs/$EXPERIMENT_NAME/ckpt_best.pth --num 3"
    echo "2. Deploy mobile model: Use files in mobile_models/$EXPERIMENT_NAME/"
    echo "3. Fine-tune if needed: Adjust hyperparameters and retrain"
    
else
    echo ""
    echo "❌ Training failed. Please check the error messages above."
    echo "💡 Common issues on Mac M4:"
    echo "   - Reduce batch size if out of memory"
    echo "   - Check MPS compatibility"
    echo "   - Verify dataset format"
    exit 1
fi

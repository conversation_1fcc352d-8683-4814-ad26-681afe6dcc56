#!/usr/bin/env python3
"""
System Check Script for Mac Mini M4 YOLO-NAS Setup
Verifies hardware capabilities and software compatibility
"""

import platform
import sys
import subprocess
import psutil
import torch

def check_system_info():
    """Check basic system information"""
    print("🖥️  System Information:")
    print(f"   OS: {platform.system()} {platform.release()}")
    print(f"   Architecture: {platform.machine()}")
    print(f"   Processor: {platform.processor()}")
    print(f"   Python: {sys.version}")
    
    # Check if running on Apple Silicon
    if platform.machine() == "arm64":
        print("   ✅ Running on Apple Silicon (ARM64)")
    else:
        print("   ⚠️  Not running on Apple Silicon")
    
    print()

def check_memory():
    """Check system memory"""
    memory = psutil.virtual_memory()
    print("💾 Memory Information:")
    print(f"   Total RAM: {memory.total / (1024**3):.1f} GB")
    print(f"   Available RAM: {memory.available / (1024**3):.1f} GB")
    print(f"   Used RAM: {memory.used / (1024**3):.1f} GB ({memory.percent}%)")
    
    if memory.total >= 20 * (1024**3):  # 20GB+
        print("   🚀 Excellent RAM for large batch YOLO-NAS training!")
        print("   💡 Can use batch sizes 12-16 for faster training")
    elif memory.total >= 16 * (1024**3):  # 16GB
        print("   ✅ Sufficient RAM for YOLO-NAS training")
    elif memory.total >= 8 * (1024**3):  # 8GB
        print("   ⚠️  Limited RAM - consider reducing batch size")
    else:
        print("   ❌ Insufficient RAM for optimal training")
    
    print()

def check_pytorch_mps():
    """Check PyTorch MPS support"""
    print("⚡ PyTorch MPS Check:")
    print(f"   PyTorch version: {torch.__version__}")
    print(f"   MPS available: {torch.backends.mps.is_available()}")
    print(f"   MPS built: {torch.backends.mps.is_built()}")
    
    if torch.backends.mps.is_available():
        print("   ✅ MPS backend ready for GPU acceleration!")
        
        # Test MPS performance
        try:
            device = torch.device("mps")
            x = torch.randn(1000, 1000, device=device)
            y = torch.randn(1000, 1000, device=device)
            z = torch.mm(x, y)
            print("   ✅ MPS tensor operations working")
        except Exception as e:
            print(f"   ❌ MPS test failed: {e}")
    else:
        print("   ❌ MPS not available - will use CPU only")
    
    print()

def check_storage():
    """Check available storage"""
    disk = psutil.disk_usage('/')
    print("💿 Storage Information:")
    print(f"   Total: {disk.total / (1024**3):.1f} GB")
    print(f"   Used: {disk.used / (1024**3):.1f} GB")
    print(f"   Free: {disk.free / (1024**3):.1f} GB")
    
    if disk.free >= 50 * (1024**3):  # 50GB
        print("   ✅ Sufficient storage for training")
    elif disk.free >= 20 * (1024**3):  # 20GB
        print("   ⚠️  Limited storage - monitor usage during training")
    else:
        print("   ❌ Insufficient storage for training")
    
    print()

def check_cpu():
    """Check CPU information"""
    print("🔧 CPU Information:")
    print(f"   CPU cores: {psutil.cpu_count(logical=False)} physical, {psutil.cpu_count(logical=True)} logical")
    print(f"   CPU frequency: {psutil.cpu_freq().current:.0f} MHz")
    
    # Check if M4 chip
    try:
        result = subprocess.run(['sysctl', '-n', 'machdep.cpu.brand_string'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            cpu_brand = result.stdout.strip()
            print(f"   CPU brand: {cpu_brand}")
            if "Apple" in cpu_brand:
                print("   ✅ Apple Silicon detected")
    except:
        pass
    
    print()

def check_dependencies():
    """Check if required packages are available"""
    print("📦 Dependency Check:")
    
    required_packages = [
        'torch', 'torchvision', 'torchaudio', 'super_gradients',
        'yaml', 'cv2', 'PIL', 'matplotlib', 'numpy', 'tqdm'
    ]
    
    for package in required_packages:
        try:
            if package == 'yaml':
                import yaml
            elif package == 'cv2':
                import cv2
            elif package == 'PIL':
                import PIL
            else:
                __import__(package)
            print(f"   ✅ {package}")
        except ImportError:
            print(f"   ❌ {package} - not installed")
    
    print()

def estimate_training_time():
    """Estimate training time based on system specs"""
    print("⏱️  Training Time Estimation (Mac Mini M4 - 24GB):")

    # Base estimates for YOLO-NAS S on different systems
    if torch.backends.mps.is_available():
        print("   With MPS acceleration + 24GB memory:")
        print("   - 200 epochs: ~2.5-4 hours (large batch advantage)")
        print("   - 300 epochs: ~4-6 hours")
        print("   - Batch size 12-16 recommended (memory advantage)")
        print("   - Faster convergence with larger batches")
    else:
        print("   CPU only (24GB memory):")
        print("   - 200 epochs: ~6-10 hours")
        print("   - 300 epochs: ~10-15 hours")
        print("   - Batch size 8-12 recommended")
    
    print()

def main():
    """Run all system checks"""
    print("🚀 Mac Mini M4 System Check for YOLO-NAS Training")
    print("=" * 50)
    print()
    
    check_system_info()
    check_memory()
    check_cpu()
    check_storage()
    check_pytorch_mps()
    check_dependencies()
    estimate_training_time()
    
    print("🎯 Recommendations for Mac Mini M4 (24GB):")
    print("   - Use batch size 12-16 for optimal memory usage")
    print("   - Enable MPS for GPU acceleration")
    print("   - Take advantage of large memory for faster training")
    print("   - Monitor temperature during long training sessions")
    print("   - Use image size 416x416 for mobile optimization")
    print("   - Consider mixed precision training for even better performance")
    print("   - Large batches enable better gradient estimates")

if __name__ == "__main__":
    main()

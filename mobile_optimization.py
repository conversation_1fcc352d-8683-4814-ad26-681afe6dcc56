#!/usr/bin/env python3
"""
Mobile Optimization Script for YOLO-NAS
Converts trained model to mobile-friendly formats
"""

import torch
from super_gradients.training import models
import argparse
import os

def optimize_for_mobile(model_path, model_type, num_classes, output_dir):
    """
    Optimize YOLO-NAS model for mobile deployment on Apple Silicon
    """
    # Load the trained model
    model = models.get(
        model_type,
        num_classes=num_classes,
        checkpoint_path=model_path
    )

    # Set model to evaluation mode and move to CPU for export
    model.eval()
    model = model.cpu()  # Ensure model is on CPU for export

    # Create dummy input for tracing
    dummy_input = torch.randn(1, 3, 416, 416)
    
    # Trace the model
    traced_model = torch.jit.trace(model, dummy_input)
    
    # Optimize for mobile
    optimized_model = torch.jit.optimize_for_inference(traced_model)
    
    # Save optimized model
    os.makedirs(output_dir, exist_ok=True)
    
    # Save TorchScript model
    torch.jit.save(optimized_model, os.path.join(output_dir, 'model_mobile.pt'))
    
    # Save ONNX model for broader compatibility
    torch.onnx.export(
        model,
        dummy_input,
        os.path.join(output_dir, 'model_mobile.onnx'),
        export_params=True,
        opset_version=11,
        do_constant_folding=True,
        input_names=['input'],
        output_names=['output'],
        dynamic_axes={
            'input': {0: 'batch_size'},
            'output': {0: 'batch_size'}
        }
    )
    
    print(f"Mobile-optimized models saved to: {output_dir}")
    print("Files created:")
    print("- model_mobile.pt (TorchScript)")
    print("- model_mobile.onnx (ONNX)")

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--model_path", required=True, help="Path to trained model")
    parser.add_argument("--model_type", default="yolo_nas_s", help="Model type")
    parser.add_argument("--num_classes", type=int, default=3, help="Number of classes")
    parser.add_argument("--output_dir", default="mobile_models", help="Output directory")
    
    args = parser.parse_args()
    
    optimize_for_mobile(
        args.model_path,
        args.model_type,
        args.num_classes,
        args.output_dir
    )

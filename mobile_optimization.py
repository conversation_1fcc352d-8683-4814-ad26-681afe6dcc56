#!/usr/bin/env python3
"""
Mobile Optimization Script for YOLO-NAS
Converts trained model to mobile-friendly formats
"""

import torch
from super_gradients.training import models
import argparse
import os
import numpy as np
from pathlib import Path

def optimize_for_mobile(model_path, model_type, num_classes, output_dir):
    """
    Optimize YOLO-NAS model for mobile deployment on Apple Silicon
    """
    # Load the trained model
    model = models.get(
        model_type,
        num_classes=num_classes,
        checkpoint_path=model_path
    )

    # Set model to evaluation mode and move to CPU for export
    model.eval()
    model = model.cpu()  # Ensure model is on CPU for export

    # Create dummy input for tracing
    dummy_input = torch.randn(1, 3, 416, 416)
    
    # Trace the model
    traced_model = torch.jit.trace(model, dummy_input)
    
    # Optimize for mobile
    optimized_model = torch.jit.optimize_for_inference(traced_model)
    
    # Save optimized model
    os.makedirs(output_dir, exist_ok=True)
    
    # Save TorchScript model
    torch.jit.save(optimized_model, os.path.join(output_dir, 'model_mobile.pt'))
    
    # Save ONNX model for broader compatibility
    torch.onnx.export(
        model,
        dummy_input,
        os.path.join(output_dir, 'model_mobile.onnx'),
        export_params=True,
        opset_version=11,
        do_constant_folding=True,
        input_names=['input'],
        output_names=['output'],
        dynamic_axes={
            'input': {0: 'batch_size'},
            'output': {0: 'batch_size'}
        }
    )
    
    # Get model size information
    model_size_mb = os.path.getsize(os.path.join(output_dir, 'model_mobile.pt')) / (1024 * 1024)
    onnx_size_mb = os.path.getsize(os.path.join(output_dir, 'model_mobile.onnx')) / (1024 * 1024)

    # Count parameters
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)

    print(f"✅ Mobile-optimized models saved to: {output_dir}")
    print("\n📊 Model Information:")
    print(f"   Total parameters: {total_params:,}")
    print(f"   Trainable parameters: {trainable_params:,}")
    print(f"   Model complexity: {total_params * 4 / 1024 / 1024:.1f} MB (FP32)")

    print("\n📁 Files created:")
    print(f"   - model_mobile.pt (TorchScript): {model_size_mb:.1f} MB")
    print(f"   - model_mobile.onnx (ONNX): {onnx_size_mb:.1f} MB")

    # Create deployment info file
    create_deployment_info(output_dir, model_type, num_classes, total_params, model_size_mb)

    print("\n🚀 Mobile deployment ready!")
    print("💡 Next steps:")
    print("   1. Test inference with test_mobile_model.py")
    print("   2. Integrate model_mobile.pt into your mobile app")
    print("   3. Use ONNX model for cross-platform deployment")

def create_deployment_info(output_dir, model_type, num_classes, total_params, model_size_mb):
    """Create deployment information file"""
    info_content = f"""# Mobile Model Deployment Information

## Model Details
- **Model Type**: {model_type}
- **Number of Classes**: {num_classes}
- **Total Parameters**: {total_params:,}
- **Model Size**: {model_size_mb:.1f} MB
- **Input Size**: 416x416x3
- **Output Format**: YOLO format (boxes, scores, classes)

## Files
- `model_mobile.pt`: TorchScript model for PyTorch Mobile
- `model_mobile.onnx`: ONNX model for cross-platform deployment
- `deployment_info.md`: This information file

## Usage Examples

### PyTorch Mobile (iOS/Android)
```python
import torch

# Load model
model = torch.jit.load('model_mobile.pt')
model.eval()

# Prepare input (416x416x3, normalized 0-1)
input_tensor = torch.randn(1, 3, 416, 416)

# Inference
with torch.no_grad():
    output = model(input_tensor)
```

### ONNX Runtime
```python
import onnxruntime as ort

# Load model
session = ort.InferenceSession('model_mobile.onnx')

# Prepare input
input_data = np.random.randn(1, 3, 416, 416).astype(np.float32)

# Inference
outputs = session.run(None, {{'input': input_data}})
```

## Performance Expectations
- **Inference Time**: 30-50ms on modern mobile devices
- **Memory Usage**: ~{model_size_mb * 2:.0f}MB during inference
- **Accuracy**: Should maintain >95% of original model accuracy

## Integration Notes
- Preprocess images to 416x416 pixels
- Normalize pixel values to [0, 1] range
- Apply NMS post-processing to filter detections
- Consider quantization for further size reduction
"""

    with open(os.path.join(output_dir, 'deployment_info.md'), 'w') as f:
        f.write(info_content)

def create_test_script(output_dir):
    """Create a test script for the mobile model"""
    test_script = '''#!/usr/bin/env python3
"""
Test script for mobile-optimized YOLO-NAS model
"""

import torch
import cv2
import numpy as np
import time
import argparse
from pathlib import Path

def preprocess_image(image_path, target_size=416):
    """Preprocess image for model input"""
    # Read image
    img = cv2.imread(str(image_path))
    if img is None:
        raise ValueError(f"Could not read image: {image_path}")

    # Convert BGR to RGB
    img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

    # Resize while maintaining aspect ratio
    h, w = img.shape[:2]
    scale = target_size / max(h, w)
    new_w, new_h = int(w * scale), int(h * scale)

    # Resize image
    img_resized = cv2.resize(img, (new_w, new_h))

    # Pad to target size
    pad_w = target_size - new_w
    pad_h = target_size - new_h
    img_padded = np.pad(img_resized,
                       ((pad_h//2, pad_h - pad_h//2),
                        (pad_w//2, pad_w - pad_w//2),
                        (0, 0)),
                       mode='constant', constant_values=128)

    # Normalize to [0, 1] and convert to tensor
    img_tensor = torch.from_numpy(img_padded).float() / 255.0
    img_tensor = img_tensor.permute(2, 0, 1).unsqueeze(0)  # BHWC -> BCHW

    return img_tensor, scale, (pad_w//2, pad_h//2)

def test_mobile_model(model_path, test_image=None):
    """Test the mobile model"""
    print(f"🧪 Testing mobile model: {model_path}")

    # Load model
    try:
        model = torch.jit.load(model_path)
        model.eval()
        print("✅ Model loaded successfully")
    except Exception as e:
        print(f"❌ Failed to load model: {e}")
        return

    # Test with dummy input if no image provided
    if test_image is None:
        print("🔄 Testing with dummy input...")
        dummy_input = torch.randn(1, 3, 416, 416)

        # Warmup
        with torch.no_grad():
            _ = model(dummy_input)

        # Benchmark
        times = []
        for i in range(10):
            start_time = time.time()
            with torch.no_grad():
                output = model(dummy_input)
            end_time = time.time()
            times.append((end_time - start_time) * 1000)  # Convert to ms

        avg_time = np.mean(times)
        std_time = np.std(times)

        print(f"✅ Inference successful!")
        print(f"📊 Performance: {avg_time:.1f} ± {std_time:.1f} ms")
        print(f"📏 Output shape: {output.shape if hasattr(output, 'shape') else 'Multiple outputs'}")

    else:
        print(f"🖼️  Testing with image: {test_image}")
        try:
            # Preprocess image
            img_tensor, scale, padding = preprocess_image(test_image)

            # Inference
            start_time = time.time()
            with torch.no_grad():
                output = model(img_tensor)
            end_time = time.time()

            inference_time = (end_time - start_time) * 1000

            print(f"✅ Image inference successful!")
            print(f"📊 Inference time: {inference_time:.1f} ms")
            print(f"📏 Output shape: {output.shape if hasattr(output, 'shape') else 'Multiple outputs'}")

        except Exception as e:
            print(f"❌ Image inference failed: {e}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Test mobile YOLO-NAS model")
    parser.add_argument("--model", default="model_mobile.pt", help="Path to mobile model")
    parser.add_argument("--image", help="Test image path (optional)")

    args = parser.parse_args()

    if not Path(args.model).exists():
        print(f"❌ Model file not found: {args.model}")
        exit(1)

    test_mobile_model(args.model, args.image)
'''

    with open(os.path.join(output_dir, 'test_mobile_model.py'), 'w') as f:
        f.write(test_script)

    # Make it executable
    import stat
    script_path = Path(output_dir) / 'test_mobile_model.py'
    script_path.chmod(script_path.stat().st_mode | stat.S_IEXEC)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Optimize YOLO-NAS for mobile deployment")
    parser.add_argument("--model_path", required=True, help="Path to trained model")
    parser.add_argument("--model_type", default="yolo_nas_s", help="Model type")
    parser.add_argument("--num_classes", type=int, default=3, help="Number of classes")
    parser.add_argument("--output_dir", default="mobile_models", help="Output directory")
    parser.add_argument("--create_test", action="store_true", help="Create test script")

    args = parser.parse_args()

    print("🚀 Starting mobile optimization for Apple Silicon...")
    print(f"📁 Input model: {args.model_path}")
    print(f"📱 Output directory: {args.output_dir}")

    optimize_for_mobile(
        args.model_path,
        args.model_type,
        args.num_classes,
        args.output_dir
    )

    if args.create_test:
        print("\n🧪 Creating test script...")
        create_test_script(args.output_dir)
        print("✅ Test script created: test_mobile_model.py")

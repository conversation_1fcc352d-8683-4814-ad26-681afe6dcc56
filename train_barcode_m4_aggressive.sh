#!/bin/bash

# Aggressive YOLO-NAS S Training Script for Mac Mini M4 (24GB)
# Takes full advantage of 24GB unified memory for maximum performance

set -e

echo "🚀 Starting AGGRESSIVE YOLO-NAS S training for Mac Mini M4 (24GB)..."
echo "💪 Utilizing full memory capacity for maximum performance"

# Check if UV environment is activated
if [[ "$VIRTUAL_ENV" == "" ]]; then
    echo "📦 Activating UV virtual environment..."
    source .venv/bin/activate
fi

# Verify we're in the right environment
echo "🐍 Python: $(which python)"
echo "📍 Environment: $VIRTUAL_ENV"

# Check MPS availability
echo "🧪 Checking MPS availability..."
python -c "
import torch
print(f'PyTorch version: {torch.__version__}')
print(f'MPS available: {torch.backends.mps.is_available()}')
print(f'MPS built: {torch.backends.mps.is_built()}')
if torch.backends.mps.is_available():
    print('✅ MPS backend ready!')
    device = 'mps'
else:
    print('⚠️  MPS not available, using CPU')
    device = 'cpu'
"

# AGGRESSIVE training parameters for 24GB memory
BATCH_SIZE=16  # Maximum batch size for 24GB
EPOCHS=250     # Extended training for best results
IMAGE_SIZE=416 # Mobile-optimized size
MODEL_TYPE="yolo_nas_s"
EXPERIMENT_NAME="barcode_m4_aggressive_24gb"

# Aggressive learning parameters
INITIAL_LR=0.0015  # Higher LR for large batch
WEIGHT_DECAY=0.0005
WARMUP_EPOCHS=8    # Extended warmup for large batch
WORKERS=10         # Maximum workers for M4

echo ""
echo "💥 AGGRESSIVE Training Configuration for Mac Mini M4 (24GB):"
echo "- Model: $MODEL_TYPE"
echo "- Batch Size: $BATCH_SIZE (MAXIMUM for 24GB memory)"
echo "- Epochs: $EPOCHS (Extended for best results)"
echo "- Image Size: ${IMAGE_SIZE}x${IMAGE_SIZE}"
echo "- Experiment Name: $EXPERIMENT_NAME"
echo "- Initial LR: $INITIAL_LR (Scaled for large batch)"
echo "- Workers: $WORKERS (Maximum utilization)"
echo "- Memory Strategy: AGGRESSIVE - Full 24GB utilization"
echo "- Expected Training Time: ~3-5 hours"
echo ""

# Check if data.yaml exists
if [[ ! -f "data.yaml" ]]; then
    echo "❌ data.yaml not found. Please ensure it's in the current directory."
    exit 1
fi

# Check if dataset exists
if [[ ! -d "Barcodes.v5i.coco" ]]; then
    echo "❌ Barcodes.v5i.coco dataset not found. Please ensure it's in the current directory."
    exit 1
fi

echo "✅ Dataset found: Barcodes.v5i.coco"
echo "✅ Configuration file: data.yaml"
echo ""

# Memory usage warning
echo "⚠️  AGGRESSIVE MODE WARNINGS:"
echo "   - This will use most of your 24GB memory"
echo "   - Close other applications for best performance"
echo "   - Monitor system temperature"
echo "   - Training will be faster but more resource intensive"
echo ""

read -p "Continue with aggressive training? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Training cancelled. Use train_barcode_m4.sh for conservative settings."
    exit 1
fi

# Start aggressive training
echo "🔥 Starting AGGRESSIVE training with maximum memory utilization..."
python train.py \
    --data data.yaml \
    --model $MODEL_TYPE \
    --batch $BATCH_SIZE \
    --epoch $EPOCHS \
    --size $IMAGE_SIZE \
    --name $EXPERIMENT_NAME \
    --worker $WORKERS \
    --initial_lr $INITIAL_LR \
    --lr_mode cosine \
    --cosine_final_lr_ratio 0.005 \
    --optimizer AdamW \
    --weight_decay $WEIGHT_DECAY \
    --warmup_mode linear_epoch_step \
    --warmup_initial_lr 1e-6 \
    --lr_warmup_epochs $WARMUP_EPOCHS \
    --mps

# Check if training completed successfully
if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 AGGRESSIVE training completed successfully!"
    echo "📁 Best model saved at: runs/$EXPERIMENT_NAME/ckpt_best.pth"
    echo "📁 Latest model saved at: runs/$EXPERIMENT_NAME/ckpt_latest.pth"
    
    # Display model info
    echo ""
    echo "📊 Model Performance Analysis:"
    python -c "
import os
import torch
from super_gradients.training import models

model_path = 'runs/$EXPERIMENT_NAME/ckpt_best.pth'
if os.path.exists(model_path):
    model = models.get('$MODEL_TYPE', num_classes=3, checkpoint_path=model_path)
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f'🔢 Total parameters: {total_params:,}')
    print(f'🎯 Trainable parameters: {trainable_params:,}')
    print(f'💾 Model size: ~{total_params * 4 / 1024 / 1024:.1f} MB (FP32)')
    print(f'⚡ Training advantage: Large batch size improved gradient estimates')
    print(f'🚀 Expected mobile performance: 30-50ms inference')
else:
    print('❌ Model file not found')
"
    
    # Run mobile optimization with aggressive settings
    echo ""
    echo "📱 Optimizing model for mobile deployment (aggressive mode)..."
    python mobile_optimization.py \
        --model_path runs/$EXPERIMENT_NAME/ckpt_best.pth \
        --model_type $MODEL_TYPE \
        --num_classes 3 \
        --output_dir mobile_models/$EXPERIMENT_NAME \
        --create_test
    
    echo ""
    echo "✅ AGGRESSIVE training and optimization completed!"
    echo "📱 Mobile models available in: mobile_models/$EXPERIMENT_NAME/"
    echo ""
    echo "🏆 AGGRESSIVE MODE RESULTS:"
    echo "   ✅ Utilized full 24GB memory capacity"
    echo "   ✅ Large batch training for better convergence"
    echo "   ✅ Extended training for maximum accuracy"
    echo "   ✅ Mobile-optimized model ready for deployment"
    echo ""
    echo "🎯 Next steps:"
    echo "1. Test the model: python inference.py --model $MODEL_TYPE --weight runs/$EXPERIMENT_NAME/ckpt_best.pth --num 3"
    echo "2. Compare with conservative training results"
    echo "3. Deploy the best performing model"
    
else
    echo ""
    echo "❌ AGGRESSIVE training failed. Please check the error messages above."
    echo "💡 Troubleshooting for 24GB aggressive mode:"
    echo "   - Reduce batch size to 12 if memory issues persist"
    echo "   - Check system temperature (aggressive mode generates more heat)"
    echo "   - Ensure no other memory-intensive applications are running"
    echo "   - Try the conservative script: ./train_barcode_m4.sh"
    exit 1
fi

#!/bin/bash

# YOLO-NAS S Environment Setup for Mac Mini M4 (Apple Silicon)
# Optimized for ARM64 architecture with MPS backend

set -e  # Exit on any error

echo "🚀 Setting up YOLO-NAS S environment for Mac Mini M4..."
echo "Architecture: $(uname -m)"
echo "OS: $(uname -s)"

# Check if we're on Apple Silicon
if [[ $(uname -m) != "arm64" ]]; then
    echo "⚠️  Warning: This script is optimized for Apple Silicon (ARM64)"
    echo "Current architecture: $(uname -m)"
fi

# Check UV installation
if ! command -v uv &> /dev/null; then
    echo "❌ UV not found. Installing UV..."
    curl -LsSf https://astral.sh/uv/install.sh | sh
    source $HOME/.cargo/env
else
    echo "✅ UV found: $(which uv)"
fi

# Create project directory if not exists
PROJECT_NAME="yolo-nas-barcode-m4"
echo "📁 Creating UV project: $PROJECT_NAME"

# Initialize UV project with Python 3.11 (recommended for Apple Silicon)
uv init $PROJECT_NAME --python 3.11
cd $PROJECT_NAME

# Copy existing files to new project structure
echo "📋 Setting up project structure..."
cp ../data.yaml .
cp ../train.py .
cp -r ../Barcodes.v5i.coco .
cp ../mobile_optimization.py .

echo "✅ Environment setup completed!"
echo "📍 Project location: $(pwd)"
echo ""
echo "Next steps:"
echo "1. cd $PROJECT_NAME"
echo "2. Run: ./install_dependencies_m4.sh"
echo "3. Run: ./train_barcode_m4.sh"

#!/bin/bash

# Setup Verification Script for Mac Mini M4 YOLO-NAS Environment
# Verifies all components are properly configured

set -e

echo "🔍 Verifying YOLO-NAS S setup for Mac Mini M4..."
echo "================================================"

# Check system architecture
echo "🖥️  System Check:"
if [[ $(uname -m) == "arm64" ]]; then
    echo "   ✅ Running on Apple Silicon (ARM64)"
else
    echo "   ❌ Not running on Apple Silicon"
    exit 1
fi

# Check UV installation
echo ""
echo "📦 UV Package Manager:"
if command -v uv &> /dev/null; then
    echo "   ✅ UV found: $(uv --version)"
else
    echo "   ❌ UV not found. Installing..."
    curl -LsSf https://astral.sh/uv/install.sh | sh
    source $HOME/.cargo/env
fi

# Check required files
echo ""
echo "📁 Required Files:"
required_files=(
    "data.yaml"
    "train.py"
    "mobile_optimization.py"
    "check_m4_system.py"
    "train_barcode_m4.sh"
    "install_dependencies_m4.sh"
    "pyproject.toml"
)

for file in "${required_files[@]}"; do
    if [[ -f "$file" ]]; then
        echo "   ✅ $file"
    else
        echo "   ❌ $file - missing"
    fi
done

# Check dataset
echo ""
echo "📊 Dataset Check:"
if [[ -d "Barcodes.v5i.coco" ]]; then
    echo "   ✅ Barcodes.v5i.coco directory found"
    
    # Check subdirectories
    for subdir in train valid test; do
        if [[ -d "Barcodes.v5i.coco/$subdir" ]]; then
            image_count=$(find "Barcodes.v5i.coco/$subdir" -name "*.jpg" -o -name "*.png" -o -name "*.jpeg" | wc -l)
            echo "   ✅ $subdir: $image_count images"
        else
            echo "   ❌ $subdir directory missing"
        fi
    done
    
    # Check annotation files
    for split in train valid test; do
        if [[ -f "Barcodes.v5i.coco/$split/_annotations.coco.json" ]]; then
            echo "   ✅ $split annotations found"
        else
            echo "   ❌ $split annotations missing"
        fi
    done
else
    echo "   ❌ Barcodes.v5i.coco directory not found"
    echo "   💡 Please ensure your dataset is in the correct location"
fi

# Check if virtual environment exists
echo ""
echo "🐍 Python Environment:"
if [[ -d ".venv" ]]; then
    echo "   ✅ UV virtual environment found"
    
    # Activate and check Python
    source .venv/bin/activate
    echo "   ✅ Python: $(python --version)"
    echo "   ✅ Environment: $VIRTUAL_ENV"
    
    # Check key packages
    echo ""
    echo "📚 Package Check:"
    python -c "
import sys
packages = ['torch', 'torchvision', 'super_gradients', 'yaml', 'cv2']
for pkg in packages:
    try:
        if pkg == 'yaml':
            import yaml
        elif pkg == 'cv2':
            import cv2
        else:
            __import__(pkg)
        print(f'   ✅ {pkg}')
    except ImportError:
        print(f'   ❌ {pkg} - not installed')
        sys.exit(1)
"
    
    # Check MPS
    echo ""
    echo "⚡ MPS Check:"
    python -c "
import torch
print(f'   PyTorch: {torch.__version__}')
print(f'   MPS Available: {torch.backends.mps.is_available()}')
print(f'   MPS Built: {torch.backends.mps.is_built()}')
if torch.backends.mps.is_available():
    print('   ✅ MPS ready for GPU acceleration!')
else:
    print('   ⚠️  MPS not available - will use CPU')
"
    
else
    echo "   ❌ Virtual environment not found"
    echo "   💡 Run: ./install_dependencies_m4.sh"
fi

echo ""
echo "🎯 Setup Summary:"
echo "=================="

# Final recommendations
if [[ -d ".venv" ]] && [[ -d "Barcodes.v5i.coco" ]] && [[ -f "data.yaml" ]]; then
    echo "✅ Environment is ready for training!"
    echo ""
    echo "🚀 Next steps:"
    echo "1. Run system check: python check_m4_system.py"
    echo "2. Start training: ./train_barcode_m4.sh"
    echo "3. Monitor progress in runs/ directory"
    echo ""
    echo "💡 Training tips for Mac Mini M4:"
    echo "   - Use batch size 4-6 for optimal performance"
    echo "   - Training will take ~3-5 hours with MPS"
    echo "   - Monitor system temperature during training"
else
    echo "❌ Setup incomplete. Please address the issues above."
    echo ""
    echo "🔧 Quick fixes:"
    echo "1. Install dependencies: ./install_dependencies_m4.sh"
    echo "2. Verify dataset location: ls -la Barcodes.v5i.coco/"
    echo "3. Check data.yaml configuration"
fi

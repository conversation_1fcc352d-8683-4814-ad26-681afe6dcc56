#!/bin/bash

# YOLO-NAS S Mobile Training Script for Barcode Detection
# Optimized for mobile deployment

echo "Starting YOLO-NAS S training for barcode detection (mobile-optimized)..."

# Check if virtual environment is activated
if [[ "$VIRTUAL_ENV" == "" ]]; then
    echo "Warning: Virtual environment not detected. Please activate your environment first."
    echo "Run: source yolo-nas-env/bin/activate"
    exit 1
fi

# Training parameters optimized for mobile deployment
BATCH_SIZE=8
EPOCHS=200
IMAGE_SIZE=416
MODEL_TYPE="yolo_nas_s"
EXPERIMENT_NAME="barcode_mobile_v1"

# Advanced parameters for mobile optimization
INITIAL_LR=0.001
WEIGHT_DECAY=0.0005
WARMUP_EPOCHS=5
WORKERS=4

echo "Training Configuration:"
echo "- Model: $MODEL_TYPE"
echo "- Batch Size: $BATCH_SIZE"
echo "- Epochs: $EPOCHS"
echo "- Image Size: ${IMAGE_SIZE}x${IMAGE_SIZE}"
echo "- Experiment Name: $EXPERIMENT_NAME"

# Start training
python3 train.py \
    --data data.yaml \
    --model $MODEL_TYPE \
    --batch $BATCH_SIZE \
    --epoch $EPOCHS \
    --size $IMAGE_SIZE \
    --name $EXPERIMENT_NAME \
    --worker $WORKERS \
    --initial_lr $INITIAL_LR \
    --lr_mode cosine \
    --cosine_final_lr_ratio 0.01 \
    --optimizer AdamW \
    --weight_decay $WEIGHT_DECAY \
    --warmup_mode linear_epoch_step \
    --warmup_initial_lr 1e-6 \
    --lr_warmup_epochs $WARMUP_EPOCHS

# Check if training completed successfully
if [ $? -eq 0 ]; then
    echo "Training completed successfully!"
    echo "Best model saved at: runs/$EXPERIMENT_NAME/ckpt_best.pth"
    
    # Run mobile optimization
    echo "Optimizing model for mobile deployment..."
    python3 mobile_optimization.py \
        --model_path runs/$EXPERIMENT_NAME/ckpt_best.pth \
        --model_type $MODEL_TYPE \
        --num_classes 3 \
        --output_dir mobile_models/$EXPERIMENT_NAME
    
    echo "Mobile optimization completed!"
    echo "Mobile models available in: mobile_models/$EXPERIMENT_NAME/"
else
    echo "Training failed. Please check the error messages above."
    exit 1
fi

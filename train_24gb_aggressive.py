#!/usr/bin/env python3
"""
🚀 24GB Mac Mini M4 Aggressive YOLO-NAS Training Script
Optimized for maximum hardware utilization with MPS acceleration
"""

import argparse
import torch
import time
import yaml
import json
import os
from super_gradients.training.models.detection_models.pp_yolo_e import PPYoloEPostPredictionCallback
from super_gradients.training.datasets.detection_datasets.coco_format_detection import COCOFormatDetectionDataset
from super_gradients.training.transforms.transforms import DetectionMosaic, DetectionRandomAffine, DetectionHSV, \
    DetectionHorizontalFlip, DetectionPaddedRescale, DetectionStandardize, DetectionTargetsFormatTransform
from super_gradients.training.datasets.datasets_utils import worker_init_reset_seed
from super_gradients.training.utils.detection_utils import CrowdDetectionCollateFN
from super_gradients.training.metrics import DetectionMetrics_050
from super_gradients.training.losses import PPYoloELoss
from super_gradients.training import dataloaders
from super_gradients.training import Trainer
from super_gradients.training import models

# Fix for PyTorch 2.6+ weights_only security issue with super-gradients
import torch.serialization

# Store original torch.load function
original_torch_load = torch.load

def patched_torch_load(f, map_location=None, pickle_module=None, weights_only=None, mmap=None, **kwargs):
    """
    Patched torch.load that handles PyTorch 2.6+ weights_only changes.
    For super-gradients compatibility, we use weights_only=False by default.
    """
    # If weights_only is not explicitly set, use False for compatibility
    if weights_only is None:
        weights_only = False
    
    try:
        return original_torch_load(f, map_location=map_location, pickle_module=pickle_module, 
                                 weights_only=weights_only, mmap=mmap, **kwargs)
    except Exception as e:
        if "weights_only" in str(e) and weights_only is True:
            print(f"[INFO] Retrying with weights_only=False for compatibility...")
            return original_torch_load(f, map_location=map_location, pickle_module=pickle_module, 
                                     weights_only=False, mmap=mmap, **kwargs)
        else:
            raise e

# Apply torch.load patch
torch.load = patched_torch_load
print("[INFO] ✅ PyTorch 2.6+ torch.load compatibility patch applied")

# Also add YOLO-NAS classes to safe globals as backup
try:
    from super_gradients.training.models.detection_models.yolo_nas.yolo_nas_variants import YoloNAS_S, YoloNAS_M, YoloNAS_L
    torch.serialization.add_safe_globals([
        YoloNAS_S,
        YoloNAS_M, 
        YoloNAS_L
    ])
    print("[INFO] ✅ YOLO-NAS classes added to PyTorch safe globals")
except ImportError:
    print("[WARNING] Could not import YOLO-NAS variants for safe globals")

# Additional patch for super-gradients checkpoint_utils if it gets imported later
def patch_super_gradients_checkpoint_utils():
    """Patch super-gradients checkpoint utils after import"""
    try:
        from super_gradients.training.utils import checkpoint_utils
        original_read_ckpt_state_dict = checkpoint_utils.read_ckpt_state_dict
        
        def patched_read_ckpt_state_dict(ckpt_path, device="cpu"):
            """Patched version that uses weights_only=False"""
            try:
                state_dict = torch.load(ckpt_path, map_location=device, weights_only=False)
                return state_dict
            except Exception as e:
                print(f"[WARNING] Checkpoint loading failed: {e}")
                return original_read_ckpt_state_dict(ckpt_path, device)
        
        checkpoint_utils.read_ckpt_state_dict = patched_read_ckpt_state_dict
        print("[INFO] ✅ super-gradients checkpoint_utils patched")
    except ImportError:
        pass

# Apply additional patch
patch_super_gradients_checkpoint_utils()

def print_system_info():
    """Print system information and optimization status"""
    print("=" * 80)
    print("🚀 24GB Mac Mini M4 AGGRESSIVE YOLO-NAS Training")
    print("=" * 80)
    print(f"PyTorch version: {torch.__version__}")
    print(f"MPS available: {torch.backends.mps.is_available()}")
    print(f"MPS built: {torch.backends.mps.is_built()}")
    
    if torch.backends.mps.is_available():
        print("✅ MPS (Metal Performance Shaders) acceleration ENABLED")
        # Note: empty_cache() not available in PyTorch 2.7.0 MPS
        print("✅ MPS ready for optimal memory usage")
    else:
        print("⚠️  MPS not available - falling back to CPU")
    
    print("=" * 80)
    print("🎯 24GB AGGRESSIVE CONFIGURATION:")
    print("   • Batch Size: 32 (MPS optimized)")
    print("   • Workers: 10 (Mac Mini M4 optimal)")
    print("   • Mixed Precision: Enabled")
    print("   • Persistent Workers: Enabled")
    print("   • Prefetch Factor: 4 (MPS optimized)")
    print("   • Pin Memory: Disabled (MPS limitation)")
    print("=" * 80)

def get_24gb_aggressive_params():
    """Get aggressive training parameters optimized for 24GB Mac Mini M4 with MPS"""
    return {
        'batch_size': 32,  # MPS optimized batch size for 24GB
        'workers': 10,     # Optimal workers for Mac Mini M4
        'prefetch_factor': 4,  # MPS optimized prefetching
        'persistent_workers': True,
        'pin_memory': False,  # Not supported on MPS yet
        'mixed_precision': True,
        'ema': True,
        'ema_decay': 0.9999,  # More aggressive EMA
        'warmup_epochs': 5,   # Extended warmup for stability
        'initial_lr': 1e-3,   # Higher learning rate
        'weight_decay': 5e-4, # Stronger regularization
    }

def create_aggressive_dataloader_params(batch_size, workers, prefetch_factor):
    """Create optimized dataloader parameters for 24GB memory with MPS"""
    return {
        "shuffle": True,
        "batch_size": batch_size,
        "num_workers": workers,
        "drop_last": True,  # Consistent batch sizes
        "pin_memory": False,  # Not supported on MPS yet
        "collate_fn": CrowdDetectionCollateFN(),
        "worker_init_fn": worker_init_reset_seed,
        "persistent_workers": workers > 0,  # Only if workers > 0
        "prefetch_factor": prefetch_factor if workers > 0 else None,
        "min_samples": 1024  # Larger minimum for stability
    }

def main():
    print_system_info()
    
    # Argument parser with aggressive defaults
    ap = argparse.ArgumentParser(description="24GB Mac Mini M4 Aggressive YOLO-NAS Training")
    ap.add_argument("-i", "--data", type=str, required=True, help="path to data.yaml")
    ap.add_argument("-n", "--name", type=str, default="24gb_aggressive", help="Checkpoint dir name")
    ap.add_argument("-b", "--batch", type=int, default=32, help="Training batch size (MPS optimized)")
    ap.add_argument("-e", "--epoch", type=int, default=200, help="Training epochs (extended for better results)")
    ap.add_argument("-j", "--worker", type=int, default=10, help="Number of workers (M4 optimized)")
    ap.add_argument("-m", "--model", type=str, default='yolo_nas_s',
                    choices=['yolo_nas_s', 'yolo_nas_m', 'yolo_nas_l'],
                    help="Model type")
    ap.add_argument("-w", "--weight", type=str, default='coco', help="Pre-trained weights")
    ap.add_argument("-s", "--size", type=int, default=640, help="Input image size")
    ap.add_argument("--resume", action='store_true', help="Resume training")
    ap.add_argument("--force-mps", action='store_true', help="Force MPS usage")
    
    args = vars(ap.parse_args())
    
    # Get aggressive parameters
    aggressive_params = get_24gb_aggressive_params()
    
    # Override with aggressive defaults if not specified
    if args['batch'] == 32:  # Using default
        args['batch'] = aggressive_params['batch_size']
    if args['worker'] == 10:  # Using default
        args['worker'] = aggressive_params['workers']
    
    print(f"🎯 TRAINING CONFIGURATION:")
    print(f"   Model: {args['model']}")
    print(f"   Batch Size: {args['batch']}")
    print(f"   Workers: {args['worker']}")
    print(f"   Epochs: {args['epoch']}")
    print(f"   Image Size: {args['size']}")
    print("=" * 80)
    
    # Setup trainer with optimal device
    if torch.backends.mps.is_available() or args['force_mps']:
        print('🚀 Training on Apple Silicon MPS (Metal Performance Shaders)')
        trainer = Trainer(experiment_name=args['name'], ckpt_root_dir='runs', device='mps')
    else:
        print('⚠️  Fallback: Training on CPU')
        trainer = Trainer(experiment_name=args['name'], ckpt_root_dir='runs', device='cpu')
    
    # Load dataset configuration
    yaml_params = yaml.safe_load(open(args['data'], 'r'))
    with open(os.path.join(yaml_params['Dir'], yaml_params['labels']['train'])) as f:
        no_class = len(json.load(f)['categories'])
    
    print(f"📊 Dataset: {no_class} classes detected")
    
    # Create aggressive training dataset with enhanced augmentations
    trainset = COCOFormatDetectionDataset(
        data_dir=yaml_params['Dir'],
        images_dir=yaml_params['images']['train'],
        json_annotation_file=yaml_params['labels']['train'],
        input_dim=(args['size'], args['size']),
        ignore_empty_annotations=False,
        transforms=[
            DetectionMosaic(prob=1.0, input_dim=(args['size'], args['size'])),
            DetectionRandomAffine(degrees=10., scales=(0.4, 1.6), shear=2.,
                                target_size=(args['size'], args['size']),
                                filter_box_candidates=False, border_value=128),
            DetectionHSV(prob=1., hgain=10, vgain=40, sgain=40),  # More aggressive
            DetectionHorizontalFlip(prob=0.5),
            DetectionPaddedRescale(input_dim=(args['size'], args['size']), max_targets=300),
            DetectionStandardize(max_value=255),
            DetectionTargetsFormatTransform(max_targets=300, input_dim=(args['size'], args['size']),
                                          output_format="LABEL_CXCYWH")
        ]
    )
    
    # Aggressive training dataloader
    train_dataloader_params = create_aggressive_dataloader_params(
        args['batch'], args['worker'], aggressive_params['prefetch_factor']
    )
    
    # Validation dataset
    valset = COCOFormatDetectionDataset(
        data_dir=yaml_params['Dir'],
        images_dir=yaml_params['images']['val'],
        json_annotation_file=yaml_params['labels']['val'],
        input_dim=(args['size'], args['size']),
        ignore_empty_annotations=False,
        transforms=[
            DetectionPaddedRescale(input_dim=(args['size'], args['size']), max_targets=300),
            DetectionStandardize(max_value=255),
            DetectionTargetsFormatTransform(max_targets=300, input_dim=(args['size'], args['size']),
                                          output_format="LABEL_CXCYWH")
        ]
    )
    
    # Validation dataloader with 2x batch size
    val_dataloader_params = create_aggressive_dataloader_params(
        args['batch'] * 2, args['worker'], aggressive_params['prefetch_factor']
    )
    val_dataloader_params['shuffle'] = False
    val_dataloader_params['drop_last'] = False
    
    # Load model
    if args['resume'] or args["weight"].endswith('.pth'):
        model = models.get(args['model'], num_classes=no_class, checkpoint_path=args["weight"])
    else:
        model = models.get(args['model'], num_classes=no_class, pretrained_weights=args["weight"])
    
    print("🚀 Starting 24GB Aggressive Training...")
    print("=" * 80)
    
    # Start training
    start_time = time.time()
    
    # Create data loaders
    train_loader = dataloaders.get(dataset=trainset, dataloader_params=train_dataloader_params)
    valid_loader = dataloaders.get(dataset=valset, dataloader_params=val_dataloader_params)
    
    # 24GB Aggressive Training Parameters
    train_params = {
        'silent_mode': False,
        "average_best_models": True,
        "warmup_mode": "linear_epoch_step",
        "warmup_initial_lr": 1e-6,
        "lr_warmup_epochs": aggressive_params['warmup_epochs'],
        "initial_lr": aggressive_params['initial_lr'],
        "lr_mode": "cosine",
        "cosine_final_lr_ratio": 0.05,  # More aggressive final LR
        "optimizer": "AdamW",
        "optimizer_params": {"weight_decay": aggressive_params['weight_decay']},
        "zero_weight_decay_on_bias_and_bn": True,
        "ema": aggressive_params['ema'],
        "ema_params": {"decay": aggressive_params['ema_decay'], "decay_type": "threshold"},
        "max_epochs": args['epoch'],
        "mixed_precision": aggressive_params['mixed_precision'],
        "loss": PPYoloELoss(use_static_assigner=False, num_classes=no_class, reg_max=16),
        "valid_metrics_list": [
            DetectionMetrics_050(
                score_thres=0.1, top_k_predictions=300, num_cls=no_class,
                normalize_targets=True,
                post_prediction_callback=PPYoloEPostPredictionCallback(
                    score_threshold=0.01, nms_top_k=1000,
                    max_predictions=300, nms_threshold=0.7
                )
            )
        ],
        "metric_to_watch": 'mAP@0.50'
    }

    if args['resume']:
        train_params['resume'] = True

    print(f"✅ Training setup complete! Starting {args['epoch']} epochs...")
    print("🎯 Aggressive Parameters:")
    print(f"   • Learning Rate: {train_params['initial_lr']}")
    print(f"   • Weight Decay: {train_params['optimizer_params']['weight_decay']}")
    print(f"   • Mixed Precision: {train_params['mixed_precision']}")
    print(f"   • EMA Decay: {train_params['ema_params']['decay']}")
    print("=" * 80)

    # Execute training
    trainer.train(
        model=model,
        training_params=train_params,
        train_loader=train_loader,
        valid_loader=valid_loader
    )

    # Training completed
    training_time = (time.time() - start_time) / 3600
    print("=" * 80)
    print(f"🎉 24GB Aggressive Training Completed!")
    print(f"⏱️  Total Time: {training_time:.2f} hours")
    print(f"📁 Checkpoints saved in: runs/{args['name']}")
    print("=" * 80)

    # Load and evaluate best model
    best_model = models.get(args['model'], num_classes=no_class,
                           checkpoint_path=os.path.join('runs', args['name'], 'ckpt_best.pth'))

    eval_result = trainer.test(
        model=best_model, test_loader=valid_loader,
        test_metrics_list=DetectionMetrics_050(
            score_thres=0.1, top_k_predictions=300, num_cls=no_class,
            normalize_targets=True,
            post_prediction_callback=PPYoloEPostPredictionCallback(
                score_threshold=0.01, nms_top_k=1000,
                max_predictions=300, nms_threshold=0.7
            )
        )
    )

    print("🏆 FINAL RESULTS:")
    for metric, value in eval_result.items():
        print(f"   {metric}: {float(value):.4f}")
    print("=" * 80)

if __name__ == '__main__':
    main()
